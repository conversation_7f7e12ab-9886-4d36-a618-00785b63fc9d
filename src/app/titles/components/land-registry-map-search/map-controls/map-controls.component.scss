@import '~assets/sass/variables';

.map-top-right-panel {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 5px;
    justify-content: flex-end;
    align-items: flex-start;
}

.map-filter-controls {
    position: relative;
    padding: 0.4em;
    background-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 4px 8px rgba(38, 50, 56, 0.24);
    border-radius: 4px;
}

.map-bottom-right-controls {
    position: absolute;
    bottom: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    z-index: 1000;

    &__button {
        cursor: pointer;
        width: 40px;
        height: 40px;
        text-decoration: none;
        display: inline-block;
        border: none;
        background: #ffffff;
        box-shadow: 0 4px 8px rgba(38, 50, 56, 0.24);
        border-radius: 4px;
        margin: 5px;
    }

    &--auto-width {
        width: auto;
    }
}

.map-bottom-center-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0 auto 12px;
    width: max-content;
    z-index: 990;
}

.disabled.map-bottom-right-controls__button {
    opacity: 0.5;
    pointer-events: none;
}

.icon {
    display: block;
    height: 100%;
    width: 100%;
    background-position-x: 50%;
    background-position-y: 50%;
    background-repeat: no-repeat;
    justify-content: center;

    &-zoom-in {
        background-image: url("data:image/svg+xml,%3Csvg width='22' height='23' viewBox='0 0 22 23' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M9.9 0.211426C15.3676 0.211426 19.8 4.64381 19.8 10.1114C19.8 12.4487 18.9901 14.5968 17.6355 16.2903L21.6778 20.3336C22.1074 20.7632 22.1074 21.4597 21.6778 21.8892C21.2813 22.2858 20.6573 22.3163 20.2258 21.9808L20.1222 21.8892L16.0789 17.847C14.3853 19.2015 12.2373 20.0114 9.9 20.0114C4.43238 20.0114 0 15.579 0 10.1114C0 4.64381 4.43238 0.211426 9.9 0.211426ZM9.9 2.41143C5.64741 2.41143 2.2 5.85883 2.2 10.1114C2.2 14.364 5.64741 17.8114 9.9 17.8114C14.1526 17.8114 17.6 14.364 17.6 10.1114C17.6 5.85883 14.1526 2.41143 9.9 2.41143ZM9.9 5.71143C10.5075 5.71143 11 6.20391 11 6.81143V9.01143H13.2C13.8075 9.01143 14.3 9.50391 14.3 10.1114C14.3 10.7189 13.8075 11.2114 13.2 11.2114H11V13.4114C11 14.0189 10.5075 14.5114 9.9 14.5114C9.29249 14.5114 8.8 14.0189 8.8 13.4114V11.2114H6.6C5.99249 11.2114 5.5 10.7189 5.5 10.1114C5.5 9.50391 5.99249 9.01143 6.6 9.01143H8.8V6.81143C8.8 6.20391 9.29249 5.71143 9.9 5.71143Z' fill='rgba(116, 142, 206, 1)' /%3E%3C/svg%3E");
    }

    &-zoom-out {
        background-image: url("data:image/svg+xml,%3Csvg width='22' height='23' viewBox='0 0 22 23' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M9.9 0.211426C15.3676 0.211426 19.8 4.64381 19.8 10.1114C19.8 12.4487 18.9901 14.5968 17.6355 16.2903L21.6778 20.3336C22.1074 20.7632 22.1074 21.4597 21.6778 21.8892C21.2813 22.2858 20.6573 22.3163 20.2258 21.9808L20.1222 21.8892L16.0789 17.847C14.3853 19.2015 12.2373 20.0114 9.9 20.0114C4.43238 20.0114 0 15.579 0 10.1114C0 4.64381 4.43238 0.211426 9.9 0.211426ZM9.9 2.41143C5.64741 2.41143 2.2 5.85883 2.2 10.1114C2.2 14.364 5.64741 17.8114 9.9 17.8114C14.1526 17.8114 17.6 14.364 17.6 10.1114C17.6 5.85883 14.1526 2.41143 9.9 2.41143ZM13.2 9.01143C13.8075 9.01143 14.3 9.50391 14.3 10.1114C14.3 10.7189 13.8075 11.2114 13.2 11.2114H6.6C5.99249 11.2114 5.5 10.7189 5.5 10.1114C5.5 9.50391 5.99249 9.01143 6.6 9.01143H13.2Z' fill='rgba(116, 142, 206, 1)' /%3E%3C/svg%3E");
    }
}

.nowrap {
    text-wrap: nowrap;
}

.loading-spinner-container {
    margin-top: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.reset-sam-tour {
    position: absolute;
    bottom: 10px;
    left: 10px;
    width: 16px;
    height: 16px;
    color: $gray;
    cursor: pointer;
}
