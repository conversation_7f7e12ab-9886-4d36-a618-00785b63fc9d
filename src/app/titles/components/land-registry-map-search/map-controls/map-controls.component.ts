import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Observable } from 'rxjs';
import { MapControls } from '../../../types/map-controls.type';
import { MapSearchQuery, MapSearchService } from '../../../store';
import { SamStateQuery } from '../../../modules/sam/stores/sam-state/sam-state-query.service';
import { map } from 'rxjs/operators';
import { ProfileService } from '@services';
import { SamTour } from '../../../modules/sam-tour/enums/sam-tour.enum';
import { TourControllerService } from '../../../modules/sam-tour/services/tour-controller.service';
import { SamUiTourQuery } from '../../../modules/sam-tour/store/sam-ui-tour-query.service';
import { MapFilterKey } from '../../../modules/map-search/types/map-filter-options.type';

@Component({
    selector: 'avl-map-controls',
    templateUrl: './map-controls.component.html',
    styleUrls: ['./map-controls.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MapControlsComponent implements OnInit {
    @Input()
    public isLoading = false;

    @Input()
    public isSamUnavailable = true;

    @Output()
    public controlsChanged = new EventEmitter<MapControls>();

    public zoom: number;
    public isFreeholdsOn$: Observable<boolean>;
    public isLeaseholdsOn$: Observable<boolean>;
    public isFiltersVisible$: Observable<boolean>;
    public isSamDisabled$: Observable<boolean>;
    public isDisplayingPolygonsModeDisabled$: Observable<boolean>;
    public isMaxZoomAchieved$: Observable<boolean>;
    public isMinZoomAchieved$: Observable<boolean>;
    public isSelectToolsEnabled$: Observable<boolean>;
    public isSelectAllButtonDisabled$: Observable<boolean>;
    public isSamFeatureEnabled$: Observable<boolean>;
    public isSamTourStarted$: Observable<boolean>;
    public isNATenureOn$: Observable<boolean>;

    constructor(
        private readonly mapSearchQuery: MapSearchQuery,
        private readonly mapSearchService: MapSearchService,
        private readonly samStateQuery: SamStateQuery,
        private readonly profile: ProfileService,
        private readonly samTourService: TourControllerService,
        private readonly samTourQuery: SamUiTourQuery,
    ) {
    }

    public ngOnInit(): void {
        this.isSamTourStarted$ = this.samTourQuery.selectIsSamTourStarted();
        this.isSamFeatureEnabled$ = this.profile.isSamEnabled$.asObservable();
        this.isSelectAllButtonDisabled$ = this.mapSearchQuery.selectIsFeatureNotExist();
        this.isSelectToolsEnabled$ = this.samStateQuery.select('isEnabled');
        this.isFiltersVisible$ = this.mapSearchQuery.isFiltersVisible$();
        this.isSamDisabled$ = this.samStateQuery.selectIsSamDisabled();
        this.isDisplayingPolygonsModeDisabled$ = this.samStateQuery.selectIsDisplayingPolygonsModeEnabled()
            .pipe(map((value) => !value));
        this.isFreeholdsOn$ = this.mapSearchQuery.select(MapFilterKey.isFreeholdsOn);
        this.isLeaseholdsOn$ = this.mapSearchQuery.select(MapFilterKey.isLeaseholdsOn);
        this.isNATenureOn$ = this.mapSearchQuery.select(MapFilterKey.isNATenureOn);
        this.isMaxZoomAchieved$ = this.mapSearchQuery.isMaxZoomAchieved();
        this.isMinZoomAchieved$ = this.mapSearchQuery.isMinZoomAchieved();
    }

    public zoomIn(): void {
        this.mapSearchService.increaseZoom();
    }

    public zoomOut(): void {
        this.mapSearchService.decreaseZoom();
    }

    public onSamEnable(): void {
        this.mapSearchService.activateSelectToolsMode();
    }

    public onSamDisable(): void {
        this.mapSearchService.deactivateSelectToolsMode();
    }

    public onLeaseholdsFilterChanged(isLeaseholdsOn: boolean): void {
        this.mapSearchService.setIsLeaseholdsOn(isLeaseholdsOn);
    }

    public onFreeholdsFilterChanged(isFreeholdsOn: boolean): void {
        this.mapSearchService.setIsFreeholdsOn(isFreeholdsOn);
    }

    public onNATenureFilterChanged(isNATenureOn: boolean): void {
        this.mapSearchService.setIsNATenureOn(isNATenureOn);
    }

    public startSamTour(): void {
        const isToolsEnabled = this.samStateQuery.getValue().isEnabled;

        if (isToolsEnabled) {
            this.mapSearchService.deactivateSelectToolsMode();
        }

        this.samTourService.reset();
        this.samTourService.startTour(SamTour.toolsSelector);
    }
}
