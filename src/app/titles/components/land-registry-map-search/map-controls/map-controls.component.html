<div class="map-top-right-panel">
    <div
        *ngIf="isLoading"
        class="loading-spinner-container"
    >
        <avl-spinner
            [strokeWidth]="3"
            [diameter]="30"
        ></avl-spinner>
    </div>
    <div
        *ngIf="(isFiltersVisible$ | async) && (isSamDisabled$ | async)"
        class="map-filter-controls"
    >
        <div class="toggle-controls">
            <avl-toggle-control
                text="Freeholds"
                idName="freeholdsId"
                [ngModel]="isFreeholdsOn$ | async"
                [ngModelOptions]="{standalone: true}"
                (ngModelChange)="onFreeholdsFilterChanged($event)"
            ></avl-toggle-control>

            <avl-toggle-control
                text="Leaseholds"
                idName="leaseholdsId"
                [ngModel]="isLeaseholdsOn$ | async"
                [ngModelOptions]="{standalone: true}"
                (ngModelChange)="onLeaseholdsFilterChanged($event)"
            ></avl-toggle-control>

            <avl-toggle-control
                text="Caution/Rentcharge"
                idName="cautionRentchargeId"
                [ngModel]="isNATenureOn$ | async"
                [ngModelOptions]="{standalone: true}"
                (ngModelChange)="onNATenureFilterChanged($event)"
            ></avl-toggle-control>
        </div>
    </div>
</div>
<div class="map-bottom-right-controls">
    <div
        matTooltipClass="mat-tooltip tooltip__max-w330 tooltip__p8 tooltip__move-left-112 tooltip__w-200-move-left-80
        tooltip__rounded4 tooltip__text14px tooltip__gray-blue-bg"
        matTooltipPosition="above"
        matTooltip="Please zoom in further to unlock the Search Avail Map (SAM) mode."
        [matTooltipDisabled]="!isSamUnavailable"
    >
        <avl-tool-select-button
            *ngIf="isSamFeatureEnabled$ | async"
            class="map-bottom-right-controls__button map-bottom-right-controls--auto-width"
            [ngClass]="{ 'disabled': isSamUnavailable }"
            (enabled)="onSamEnable()"
            (disabled)="onSamDisable()"
        ></avl-tool-select-button>
    </div>
    <button
        class="map-bottom-right-controls__button"
        data-testid="zoom-in-button"
        type="button"
        [ngClass]="{ 'disabled': isMaxZoomAchieved$ | async }"
        [disabled]="isMaxZoomAchieved$ | async"
        (click)="zoomIn()"
    >
        <i class="icon icon-zoom-in"></i>
    </button>
    <button
        class="map-bottom-right-controls__button"
        data-testid="zoom-out-button"
        type="button"
        [ngClass]="{ 'disabled': isMinZoomAchieved$ | async }"
        [disabled]="isMinZoomAchieved$ | async"
        (click)="zoomOut()"
    >
        <i class="icon icon-zoom-out"></i>
    </button>
</div>
<div
    *ngIf="isSelectToolsEnabled$ | async"
    class="map-bottom-center-controls"
>
    <avl-select-controls
        [isSelectAllDisabled]="isSelectAllButtonDisabled$ | async"
    ></avl-select-controls>
</div>

<mat-icon
    *ngIf="isSamTourStarted$ | async"
    class="reset-sam-tour"
    svgIcon="exclamation-circle"
    matTooltipClass="mat-tooltip tooltip__min-w-200 tooltip__p8 tooltip__rounded4 tooltip__text14px
        tooltip__gray-blue-bg"
    matTooltipPosition="right"
    matTooltip="Reset the Search Avail Map UI tour."
    (click)="startSamTour()"
></mat-icon>
