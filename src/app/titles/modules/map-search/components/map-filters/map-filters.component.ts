import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
    selector: 'avl-map-filters',
    templateUrl: './map-filters.component.html',
    styleUrls: ['./map-filters.component.scss'],
})
export class MapFiltersComponent implements OnInit {
    @Input()
    public isFreehold = false;

    @Input()
    public isLeasehold = false;

    @Input()
    public isNATenure = false;

    @Output()
    public isFreeholdChanged = new EventEmitter<boolean>();

    @Output()
    public isLeaseholdChanged = new EventEmitter<boolean>();

    @Output()
    public isNATenureChanged = new EventEmitter<boolean>();

    public isExpanded = false;

    private readonly isExpandedStorageKey = 'is-filters-expanded';


    public ngOnInit(): void {
        this.loadFromLocalStorage();
    }

    public expand(): void {
        this.isExpanded = true;
        this.saveToLocalStorage();
    }

    public collapse(): void {
        this.isExpanded = false;
        this.saveToLocalStorage();
    }

    public onFreeholdChanged(isFreehold: boolean): void {
        this.isFreeholdChanged.emit(isFreehold);
    }

    public onLeaseholdChanged(isLeasehold: boolean): void {
        this.isLeaseholdChanged.emit(isLeasehold);
    }

    public onNATenureChanged(isNATenure: boolean): void {
        this.isNATenureChanged.emit(isNATenure);
    }

    private saveToLocalStorage(): void {
        localStorage.setItem(this.isExpandedStorageKey, JSON.stringify(this.isExpanded));
    }

    private loadFromLocalStorage(): void {
        this.isExpanded = localStorage.getItem(this.isExpandedStorageKey) === 'true' ?? true;
    }
}
