<div class="container">
    <button
        class="clear-btn-styles toggle-button"
        type="button"
        [attr.aria-label]="isExpanded ? 'Collapse filters' : 'Expand filters'"
        (click)="toggleExpansion()"
    >
        <div
            class="arrow"
            [class.arrow--right]="!isExpanded"
            [class.arrow--left]="isExpanded"
        ></div>
    </button>
    <div
        class="filters"
    >
        <div class="filter-wrapper">
            <avl-switch-toggle
                icon="home"
                buttonId="freehold-filter"
                [isActivated]="isFreehold"
                (toggleChanged)="onFreeholdChanged($event)"
            ></avl-switch-toggle>
            <label
                class="label"
                for="freehold-filter"
                [class.expanded]="isExpanded"
            >
                Freeholds
            </label>
        </div>
        <div class="filter-wrapper">
            <avl-switch-toggle
                icon="key"
                buttonId="leasehold-filter"
                [isActivated]="isLeasehold"
                (toggleChanged)="onLeaseholdChanged($event)"
            ></avl-switch-toggle>
            <label
                class="label"
                for="leasehold-filter"
                [class.expanded]="isExpanded"
            >
                Leaseholds
            </label>
        </div>
        <div class="filter-wrapper">
            <avl-switch-toggle
                icon="locked-home"
                buttonId="na-tenure-filter"
                [isActivated]="isNATenure"
                (toggleChanged)="onNATenureChanged($event)"
            ></avl-switch-toggle>
            <label
                class="label"
                for="na-tenure-filter"
                [class.expanded]="isExpanded"
            >
                Caution/Rentcharge
            </label>
        </div>
    </div>
</div>
