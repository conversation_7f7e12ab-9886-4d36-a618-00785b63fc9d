.container {
    display: flex;
    align-items: stretch;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 4px;
    overflow: hidden;
}

.toggle-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 15px;
    min-width: 15px;
    border: none;
    border-right: 1px solid #e0e0e0;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.toggle-button:hover {
    background-color: #eeeeee;
}

.toggle-button mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: #666666;
}

.arrow {
    width: 7px;
    height: 7px;
    border-right: 1px solid rgba(102, 102, 102, 0.3);
    border-top: 1px solid rgba(102, 102, 102, 0.3);
    transition: transform 0.2s ease;
    transform: rotate(45deg);

    &--right {
        transform: rotate(225deg);
    }

    &--left {
        transform: rotate(45deg);
    }
}

.filters {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 0.4em;
    padding: 0.4em;
    flex: 1;
}

.filter-wrapper {
    display: flex;
    align-items: center;
    gap: 0.4em;
    white-space: nowrap;
}

.label {
    background-color: transparent;
    width: 0;
    opacity: 0;
    overflow: hidden;
    white-space: nowrap;
    transition: width 0.3s ease-in-out, opacity 0.3s ease-in-out;
    margin-left: 0;
}

.label.expanded {
    width: auto;
    opacity: 1;
    margin-left: 0.4em;
}
