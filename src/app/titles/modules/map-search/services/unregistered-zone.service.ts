import { Injectable } from '@angular/core';
import { MapElementsBase } from './map-elements-base';
import { GeoJSONFeature, GeoJSONSource, Map } from 'maplibre-gl';
import { Feature, MultiPolygon, Polygon } from 'geojson';
import { cleanCoords, difference, featureCollection, simplify } from '@turf/turf';
import { MapSearchQuery } from '../../../store';
import { filter, takeUntil } from 'rxjs/operators';
import { MapFeaturesService } from './map-features.service';
import { Subject } from 'rxjs';
import { samMinZoomVisibility } from '../../../constants/land-registry-search.constants';
import { SelectionToolContextService } from '../../sam/services/selection-tool-context.service';
import { LoggerService, ProfileService } from '@services';
import { SamStateQuery } from '../../sam/stores/sam-state/sam-state-query.service';
import { SamMode } from '../../sam/enums/sam-mode.enum';
import { environment } from '@env/environment';

@Injectable()
export class UnregisteredZoneService extends MapElementsBase {
    private readonly maskSourceId = 'unregistered-zone-mask-source';
    private readonly maskLayerId = 'unregistered-zone-mask-layer';
    private readonly layerPatternId = 'hatch-pattern';
    private readonly destroy$ = new Subject<void>();
    private readonly layerStyles = {
        fillPattern: this.layerPatternId,
        fillOpacity: 0.3,
    };

    constructor(
        private readonly mapSearchQuery: MapSearchQuery,
        private readonly samStateQuery: SamStateQuery,
        private readonly mapFeaturesService: MapFeaturesService,
        private readonly selectionToolContextService: SelectionToolContextService,
        private readonly log: LoggerService,
        private readonly profile: ProfileService,
    ) {
        super();
    }

    public get isEnabled(): boolean {
        return environment.isMapUnregisteredLandMaskEnabled ?? false;
    }

    public initialize(map: Map): void {
        super.initialize(map);
        this.listenToLoadedFeaturesChange();
        this.listenToMapZoomChange();
        this.listenToMapModeChange();
        this.listenToMapFiltersChange();
    }

    public finalize(): void {
        this.destroy$.next();

        if (this.isMapInitialized() && this.map.getLayer(this.maskLayerId)) {
            this.map.removeLayer(this.maskLayerId);
        }

        if (this.isMapInitialized() && this.map.getSource(this.maskSourceId)) {
            this.map.removeSource(this.maskSourceId);
        }

        if (this.isMapInitialized() && this.map.hasImage(this.layerPatternId)) {
            this.map.removeImage(this.layerPatternId);
        }

        super.finalize();
    }

    public initMaskSource(features: GeoJSONFeature[]): void {
        const loadedAreaPolygon = this.selectionToolContextService.getLoadedAreaPolygon();
        if (!loadedAreaPolygon) {
            this.log.warn('Loaded area polygon not provided');
            return;
        }

        const mask = this.createMask(loadedAreaPolygon, features);
        if (!mask) {
            this.log.warn('Unregistered area mask was not created');
            return;
        }

        this.updateSource(mask);
    }

    public initLayer(): void {
        const isSource = this.map.getSource(this.maskSourceId);
        const isLayer = this.map.getLayer(this.maskLayerId);
        const isPattern = this.map.hasImage(this.layerPatternId);

        if (!isSource || isLayer) {
            return;
        }

        if (!isPattern) {
            this.map.loadImage('/assets/images/hatch-pattern.png', (err, img) => {
                if (err || !img) {
                    this.log.error('Failed to load hatch pattern image:', err);
                    return;
                }


                const isImage = this.map.hasImage(this.layerPatternId);
                if (!isImage) {
                    this.map.addImage(this.layerPatternId, img);
                }

                this.addMaskLayer();
            });
        } else {
            this.addMaskLayer();
        }
    }

    public hideLayer(): void {
        if (this.map.getLayer(this.maskLayerId)) {
            this.map.setLayoutProperty(this.maskLayerId, 'visibility', 'none');
        }
    }

    public showLayer(): void {
        if (this.map.getLayer(this.maskLayerId)) {
            this.map.setLayoutProperty(this.maskLayerId, 'visibility', 'visible');
        }
    }

    public clearSource(): void {
        if (this.map.getSource(this.maskSourceId)) {
            const source = this.map.getSource(this.maskSourceId) as GeoJSONSource;
            source.setData({
                type: 'FeatureCollection',
                features: [],
            });
        }
    }

    public createMask(loadedAreaPolygon: Feature<Polygon>, features: GeoJSONFeature[]): Feature<Polygon | MultiPolygon> | null {
        if (!features.length) {
            return loadedAreaPolygon;
        }

        const polygons = features as Feature<Polygon | MultiPolygon>[];

        try {
            return this.findDifference(loadedAreaPolygon, polygons);
        } catch (error) {
            this.log.warn('Creating unregistered zone mask failed:', error);

            const clearedPolygons = this.simplifyPolygon(polygons);

            try {
                return this.findDifference(loadedAreaPolygon, clearedPolygons);
            } catch (error) {
                this.log.warn('Creating unregistered zone mask (after simplification) failed:', error);
                return null;
            }
        }
    }

    private findDifference(loadedAreaPolygon: Feature<Polygon>, polygons: Feature<Polygon | MultiPolygon>[]): Feature<Polygon | MultiPolygon> | null {
        const featureCollectionInput = featureCollection([loadedAreaPolygon, ...polygons]);
        const result = difference(featureCollectionInput) as Feature<Polygon | MultiPolygon> | null;

        if (!result) {
            this.log.warn('Difference operation resulted in null - polygons may completely cover the loaded area');
        }

        return result ?? null;
    }

    private simplifyPolygon(polygons: Feature<Polygon | MultiPolygon>[]): Feature<Polygon | MultiPolygon>[] {
        return polygons.map((polygon) => {
            try {
                const simplified = simplify(polygon, { tolerance: 0.000006 });

                return cleanCoords(simplified);
            } catch (error) {
                this.log.warn('Simplifying polygon failed:', { details: error, polygon });
                return polygon;
            }
        });
    }

    private updateSource(mask: Feature<Polygon | MultiPolygon>): void {
        if (this.map.getSource(this.maskSourceId)) {
            (this.map.getSource(this.maskSourceId) as GeoJSONSource).setData(mask);
        } else {
            this.map.addSource(this.maskSourceId, { type: 'geojson', data: mask });
        }
    }

    private addMaskLayer(): void {
        if (this.map.getLayer(this.maskLayerId)) {
            return;
        }

        this.map.addLayer({
            id: this.maskLayerId,
            type: 'fill',
            source: this.maskSourceId,
            paint: {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                'fill-pattern': this.layerStyles.fillPattern,
                // eslint-disable-next-line @typescript-eslint/naming-convention
                'fill-opacity': this.layerStyles.fillOpacity,
            },
        });
    }

    private listenToLoadedFeaturesChange(): void {
        this.mapSearchQuery.select('featuresMap')
            .pipe(
                takeUntil(this.destroy$),
                filter((featuresMap) => !!featuresMap),
            )
            .subscribe((featuresMap) => {
                const features = this.mapFeaturesService.convertToArray(featuresMap);
                this.initMaskSource(features);
                this.updateMaskLayer();
            });
    }

    private listenToMapZoomChange(): void {
        this.mapSearchQuery.select('zoom')
            .pipe(
                takeUntil(this.destroy$),
            )
            .subscribe(() => {
                this.updateMaskLayer();
            });
    }

    private listenToMapModeChange(): void {
        this.samStateQuery.select('mode')
            .pipe(
                takeUntil(this.destroy$),
            )
            .subscribe(() => {
                this.clearSource();
            });
    }

    private listenToMapFiltersChange(): void {
        this.mapSearchQuery.select(['isFreeholdsOn', 'isLeaseholdsOn'])
            .pipe(
                takeUntil(this.destroy$),
            )
            .subscribe(() => {
                this.updateMaskLayer();
            });
    }

    private updateMaskLayer(): void {
        const samState = this.samStateQuery.getValue();
        const mapState = this.mapSearchQuery.getValue();
        const zoom = mapState.zoom;
        const isSamDisplayingPolygons = samState.mode === SamMode.displayingPolygons
            || samState.mode === SamMode.noToolSelected;
        const minApplicableZoom = samState.isEnabled
            ? samMinZoomVisibility
            : this.profile.mapSearchMinZoomFeatureVisibility$.getValue();
        const isZoomApplicable = zoom >= minApplicableZoom;
        const isFiltersApplicable = mapState.isFreeholdsOn || mapState.isLeaseholdsOn;

        if (isZoomApplicable && isSamDisplayingPolygons && isFiltersApplicable) {
            this.initLayer();
            this.showLayer();
        } else {
            this.hideLayer();
        }
    }
}
