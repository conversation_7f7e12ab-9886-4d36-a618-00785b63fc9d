import { TourButton } from '../types/tour-button.type';

export const defaultButtons: { [key: string]: TourButton } = {
    skip: {
        classes: 'shepherd-button-secondary skip-button',
        text: 'Skip',
        type: 'cancel',
    },
    prev: {
        classes: 'shepherd-button-secondary prev-button',
        text: 'Previous',
        type: 'back',
    },
    next: {
        classes: 'shepherd-button-primary next-button',
        text: 'Next',
        type: 'next',
    },
    close: {
        classes: 'shepherd-button-primary close-button',
        text: 'Close',
        type: 'cancel',
    },
    finish: {
        classes: 'shepherd-button-primary finish-button',
        text: 'Finish',
        type: 'cancel',
    },
};

export const nextSkip: TourButton[] = [defaultButtons.skip, defaultButtons.next];

export const prevNextSkip: TourButton[] = [defaultButtons.skip, defaultButtons.prev, defaultButtons.next];

export const prevCloseSkip: TourButton[] = [defaultButtons.skip, defaultButtons.prev, defaultButtons.close];

export const closeSkip: TourButton[] = [defaultButtons.skip, defaultButtons.close];

export const prevFinish: TourButton[] = [defaultButtons.prev, defaultButtons.finish];

export const finish: TourButton[] = [defaultButtons.finish];
