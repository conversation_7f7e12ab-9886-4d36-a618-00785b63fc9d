export const autoOffset = {
    modifiers: [
        {
            name: 'offset',
            options: {
                offset: ({ placement }) => {
                    const margin = 24;
                    const isAnySide = placement.includes('top')
                        || placement.includes('left')
                        || placement.includes('right')
                        || placement.includes('bottom');

                    return isAnySide ? [0, margin] : [];
                },
            },
        },
    ],
};
