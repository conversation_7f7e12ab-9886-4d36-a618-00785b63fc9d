import { TourBaseService } from './tour-base.service';
import Step from 'shepherd.js/src/types/step';

export abstract class TourControllerBase {

    constructor(
        protected readonly controllerId: string,
        protected readonly tourService: TourBaseService,
    ) {
        this.tourService.isTourNew(this.controllerId)
            .subscribe((isNew) => {
                const isSeen = !isNew;
                this.setIsSeen(isSeen);
                this.onSeenStatusLoaded(isSeen);
            });
    }

    public abstract setIsSeen(isSeen: boolean): void;

    public abstract setActiveStep(stepId: string | null): void;

    public abstract startTour(tour: string): void;

    public abstract finishTour(stepId: string): void;

    public abstract finishStep(stepId: string): void;

    public abstract isTourSeen(tour: string): boolean;

    public abstract isTourDone(tour: string): boolean;

    public abstract skipTour(): void;

    public abstract getLastStepCounter(): number;

    public abstract getStepsCount(): number;

    public abstract reset(): void;

    public initializeTour(steps: Step.StepOptions[]): void {
        const stepsWithShowEvent = steps.map((tourStep) => this.addStepEventHandlers(tourStep));
        this.tourService.addSteps(stepsWithShowEvent);
    }

    public isTourAvailable(tour: string | null): boolean {
        const isDone = this.isTourDone(tour);
        const isTourSeen = this.isTourSeen(tour);

        return !isDone && !isTourSeen;
    }

    public tryToStartTour(tour: string): void {
        const isTourAvailable = this.isTourAvailable(tour);
        if (isTourAvailable) {
            this.startTour(tour);
        }
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected onSeenStatusLoaded(isSeen: boolean): void {
        // No default implementation
    }

    protected addStepEventHandlers(tourStep: Step.StepOptions): Step.StepOptions {
        const stepId = tourStep.id;

        tourStep.when = {
            show: () => {
                this.setActiveStep(stepId);
                this.addSkipButtonEventListener();
            },
            hide: () => {
                this.finishStep(stepId);
                this.removeSkipButtonEventListener();
            },
        };

        return tourStep;
    }

    protected addSkipButtonEventListener(): void {
        const tour = this.tourService.tour;

        if (tour) {
            const currentStep = tour.getCurrentStep();
            const currentStepElement = currentStep?.getElement();
            const skipButton = currentStepElement?.querySelector('.shepherd-footer .skip-button');

            if (skipButton) {
                skipButton.addEventListener('click', this.skipTour.bind(this));
            }
        }
    }

    protected removeSkipButtonEventListener(): void {
        const tour = this.tourService.tour;

        if (tour) {
            const currentStep = tour.getCurrentStep();
            const currentStepElement = currentStep?.getElement();
            const skipButton = currentStepElement?.querySelector('.shepherd-footer .skip-button');

            if (skipButton) {
                skipButton.removeEventListener('click', this.skipTour.bind(this));
            }
        }
    }

    protected addStepsCounterTitle(steps: Step.StepOptions[], lastStepCounter: number, totalSteps: number): Step.StepOptions[] {
        steps.forEach((step, index) => {
            const nextStepIndex =  index + lastStepCounter + 1;
            step.title = `
              <strong>Step ${nextStepIndex > totalSteps ? totalSteps : nextStepIndex}</strong>/${totalSteps}
            `;
        });

        return steps;
    }

    protected markTourAsSeen(): void {
        this.tourService.markTourAsSeen(this.controllerId);
        this.setIsSeen(true);
    }

}
