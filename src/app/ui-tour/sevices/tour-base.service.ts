import { Injectable } from '@angular/core';
import { ShepherdService } from 'angular-shepherd';
import Step from 'shepherd.js/src/types/step';
import <PERSON> from 'shepherd.js/src/types/shepherd';
import { autoOffset } from '../constants/popover-offset.constant';
import { finish, nextSkip, prevFinish, prevNextSkip } from '../constants/tour-buttons.constant';
import { OnboardingApi } from '../../onboarding/api/onboarding.api';
import { Observable } from 'rxjs';

@Injectable()
export class TourBaseService {

    constructor(
        private readonly shepherdService: ShepherdService,
        private readonly onboardingApi: OnboardingApi,
    ) {
        this.initialize();
    }

    public get tour(): Shepherd.Tour | null {
        return this.shepherdService.tourObject ?? null;
    }

    public isTourNew(tourId: string): Observable<boolean> {
        return this.onboardingApi.checkBranchSeen(tourId);
    }

    public markTourAsSeen(tourId: string): void {
        this.onboardingApi.setBranchAsSeen(tourId).subscribe();
    }

    public back(): void {
        this.shepherdService.back();
    }

    public cancel(): void {
        const isTourActive = this.shepherdService.isActive;
        if (isTourActive) {
            this.shepherdService.cancel();
        }
    }

    public complete(): void {
        this.shepherdService.complete();
    }

    public hide(): void {
        this.shepherdService.hide();
    }

    public next(): void {
        this.shepherdService.next();
    }

    public show(id: string | number): void {
        this.shepherdService.show(id);
    }

    public start(): void {
        this.shepherdService.start();
    }

    public addSteps(steps: Step.StepOptions[]): void {
        this.shepherdService.addSteps(steps);
    }

    public addNavigationButtons(steps: Step.StepOptions[], currentStepIndex?: number, totalSteps?: number): Step.StepOptions[] {
        const stepsCount = totalSteps ?? steps.length;

        steps.forEach((step, index) => {
            const stepGlobalIndex = !currentStepIndex && currentStepIndex !== 0
                ? index
                : currentStepIndex + index;
            const isButtonExist = (step.buttons ?? []).length > 0;

            if (!isButtonExist) {
                const isFirst = index === 0;
                const isLast = stepGlobalIndex === stepsCount - 1;
                const isThereOneStep = steps.length === 1;

                if (isLast) {
                    if (isThereOneStep) {
                        step.buttons = finish;
                    } else {
                        step.buttons = prevFinish;
                    }
                } else if (isFirst) {
                    step.buttons = nextSkip;
                } else {
                    step.buttons = prevNextSkip;
                }
            }
        });

        return steps;
    }

    private initialize(): void {
        this.shepherdService.confirmCancel = false;
        this.shepherdService.defaultStepOptions = {
            classes: 'avl-ui-tour',
            scrollTo: true,
            cancelIcon: {
                enabled: true,
            },
            popperOptions: autoOffset,
            modalOverlayOpeningPadding: 5,
            modalOverlayOpeningRadius: 4,
        };
        this.shepherdService.modal = true;
    }
}
