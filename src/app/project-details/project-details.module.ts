import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProjectDetailsDialogService } from './services/project-details-dialog.service';
import { ProjectDetailsDialogComponent } from './components/project-details-dialog/project-details-dialog.component';
import { SharedModule } from '@shared/shared.module';
import { ProjectDetailsQuery } from './stores/project-details/project-details.query';
import { ProjectDetailsStore } from './stores/project-details/project-details.store';
import { ProjectsService } from './services/projects.service';
import { ProjectApi } from './api/project.api';
import { ProjectDetailsResolver } from './resolvers/project-details.resolver';


@NgModule({
    declarations: [
        ProjectDetailsDialogComponent,
    ],
    imports: [
        CommonModule,
        SharedModule,
    ],
    providers: [
        ProjectDetailsDialogService,
        ProjectDetailsStore,
        ProjectDetailsQuery,
        ProjectsService,
        ProjectApi,
        ProjectDetailsResolver,
    ],
})
export class ProjectDetailsModule {
}
