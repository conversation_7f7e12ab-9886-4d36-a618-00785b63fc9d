import { Component, Input } from '@angular/core';

@Component({
    selector: 'avl-mouse-position-popup',
    templateUrl: './mouse-position-popup.component.html',
    styleUrls: ['./mouse-position-popup.component.scss'],
})
export class MousePositionPopupComponent {
    @Input()
    public positionX: number;

    @Input()
    public positionY: number;

    @Input()
    public text = '';

    public height = 30;

    public setPosition(x: number, y: number): void {
        this.positionX = x;
        this.positionY = y - this.height;
    }

    public setContent(text: string): void {
        this.text = text;
    }
}
