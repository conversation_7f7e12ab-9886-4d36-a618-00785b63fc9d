import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MaplibreComponent } from './maplibre.component';
import { PolygonTransformationService } from './services/polygon-transformation.service';
import { GeojsonUtilsService } from './services/geojson-utils.service';
import { MousePositionPopupComponent } from './components/mouse-position-popup/mouse-position-popup.component';
import { MatIconModule } from '@angular/material/icon';
import { MousePositionPopupService } from './services/mouse-position-popup.service';


@NgModule({
    declarations: [
        MaplibreComponent,
        MousePositionPopupComponent,
    ],
    exports: [
        MaplibreComponent,
    ],
    providers: [
        PolygonTransformationService,
        GeojsonUtilsService,
        MousePositionPopupService,
    ],
    imports: [
        CommonModule,
        MatIconModule,
    ],
})
export class MaplibreModule {
}
