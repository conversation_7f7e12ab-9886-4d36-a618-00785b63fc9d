import { Component, ElementRef, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { LngLat, LngLatLike, Map, MapMouseEvent, RequestTransformFunction, StyleSpecification } from 'maplibre-gl';
import { MousePositionPopupComponent } from './components/mouse-position-popup/mouse-position-popup.component';
import { MousePositionPopupService } from './services/mouse-position-popup.service';
import { Observable } from 'rxjs';

@Component({
    selector: 'avl-maplibre',
    templateUrl: './maplibre.component.html',
    styleUrls: ['./maplibre.component.scss'],
})
export class MaplibreComponent implements OnInit, OnChanges, OnDestroy {
    @Input()
    public style: string | StyleSpecification;

    @Input()
    public zoom?: number;

    @Input()
    public maxZoom?: number;

    @Input()
    public minZoom?: number;

    @Input()
    public center?: LngLatLike;

    @Input()
    public transformRequest?: RequestTransformFunction;

    @Output()
    public mapLoad = new EventEmitter<Map>();

    @Output()
    public zoomEnd = new EventEmitter<number>();

    @Output()
    public moveEnd = new EventEmitter<LngLat>();

    @Output()
    public mapResize = new EventEmitter<void>();

    @Output()
    public mapError = new EventEmitter<ErrorEvent>();

    @ViewChild(MousePositionPopupComponent)
    public mousePositionPopupComponent: MousePositionPopupComponent;

    public isMousePositionPopupVisible$: Observable<boolean>;
    public isMouseWithinMap = false;
    private map: Map;
    private timeoutInstanceId: NodeJS.Timeout = null;

    constructor(
        private readonly elementRef: ElementRef,
        private readonly popup: MousePositionPopupService,
    ) {
        this.isMousePositionPopupVisible$ = this.popup.isVisible$;
        this.onMapMouseMove = this.onMapMouseMove.bind(this);
    }

    public ngOnInit(): void {
        this.mapInitialisation();
    }

    public ngOnChanges(changes: SimpleChanges): void {
        const zoom = !changes['zoom']?.isFirstChange() && changes['zoom']?.currentValue;
        const center = !changes['center']?.isFirstChange() && changes['center']?.currentValue;

        if (zoom) {
            this.map.zoomTo(zoom);
        }
        if (center) {
            this.map.setCenter(center);
        }
    }

    public ngOnDestroy(): void {
        this.map.off('mousemove', this.onMapMouseMove);
        this.stopStyleLoadingPolling();
    }

    public onMouseEnter(): void {
        this.isMouseWithinMap = true;
    }

    public onMouseMove(event: MouseEvent): void {
        const marginTopPx = 10;
        const marginLeftPx = 10;
        const mapBoundingClientRect = this.elementRef.nativeElement.getBoundingClientRect();
        const mapOffsetTop = mapBoundingClientRect.top;
        const mapOffsetLeft = mapBoundingClientRect.left;
        const x = event.clientX - mapOffsetLeft + marginLeftPx;
        const y = event.clientY - mapOffsetTop - marginTopPx;
        this.mousePositionPopupComponent?.setPosition(x, y);
    }

    public onMouseLeave(): void {
        this.isMouseWithinMap = false;
    }

    private mapInitialisation(): void {
        const mapContainer = document.getElementById('map');
        this.map = new Map({
            container: mapContainer,
            style: this.style,
            zoom: this.zoom,
            maxZoom: this.maxZoom,
            minZoom: this.minZoom,
            center: this.center,
            dragRotate: false,
            antialias: true,
            transformRequest: this.transformRequest,
        });

        this.listenZoomEnd();
        this.listenResize();
        this.listenLoad();
        this.listenMoveEnd();
        this.listenError();
        this.listenMapMouseMove();
    }

    private listenMapMouseMove(): void {
        this.map.on('mousemove', this.onMapMouseMove);
    }

    private listenZoomEnd(): void {
        this.map.on('zoomend', () => {
            const currentZoom = this.map.getZoom();
            this.zoomEnd.emit(currentZoom);
        });
    }

    private listenResize(): void {
        this.map.on('resize', () => {
            this.mapResize.emit();
        });
    }

    private listenLoad(): void {
        const pollingDelayMs = 100;
        const maxWaitTimeMs = 40 * 1000;
        const maxAttempts = maxWaitTimeMs / pollingDelayMs;
        let attempts = 0;

        const checkStyleAndEmit = (): void => {
            if (this.map.isStyleLoaded()) {
                this.map.off('load', callback);
                this.mapLoad.emit(this.map);
                this.stopStyleLoadingPolling();

                return;
            }

            if (++attempts >= maxAttempts) {
                const error = new ErrorEvent('error', {
                    message: `Map style failed to load after ${maxWaitTimeMs / 1000} seconds`,
                    error: new Error('Map style loading timeout'),
                });

                this.map.off('load', callback);
                this.mapError.emit(error);
                this.stopStyleLoadingPolling();

                return;
            }
        };

        const callback = (): void => {
            checkStyleAndEmit();
        };

        this.timeoutInstanceId = setInterval(checkStyleAndEmit, pollingDelayMs);
        this.map.on('load', callback);
    }

    private stopStyleLoadingPolling(): void {
        if (this.timeoutInstanceId) {
            clearInterval(this.timeoutInstanceId);
            this.timeoutInstanceId = null;
        }
    }

    private listenMoveEnd(): void {
        this.map.on('moveend', () => {
            const currentCenter = this.map.getCenter();
            this.moveEnd.emit(currentCenter);
        });
    }

    private listenError(): void {
        this.map.on('error', (error) => {
            this.mapError.emit(error);
        });
    }

    private onMapMouseMove(event: MapMouseEvent): void {
        this.updateMousePositionPopupContent(event.lngLat);
    }

    private updateMousePositionPopupContent(coordinates: LngLat): void {
        const precision = 6;
        const lng = coordinates.lng.toFixed(precision);
        const lat = coordinates.lat.toFixed(precision);
        const text = `${lng} ${lat}`;
        this.mousePositionPopupComponent?.setContent(text);
    }
}
