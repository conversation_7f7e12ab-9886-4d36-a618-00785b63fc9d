import { Injectable } from '@angular/core';
import { LngLat, Map as MaplibreMap, Marker } from 'maplibre-gl';

@Injectable()
export class GeojsonUtilsService {
    // This class is created for debugging purposes only
    public getRandomColor(): string {
        const saturatedColors: string[] = [
            'crimson',
            'ultramarine',
            'emerald',
            'magenta',
            'lime',
            'coral',
            'indigo',
            'fuchsia',
            'vermilion',
            'turquoise',
            'chartreuse',
            'byzantium',
            'scarlet',
            'blue',
            'pink',
            'green'
        ];

        return saturatedColors[Math.floor(Math.random() * 16)];
    }

    public generateRandomColor(): string {
        const letters = '0123456789ABCDEF';
        let color = '#';

        for (let i = 0; i < 6; i++) {
            color += letters[Math.floor(Math.random() * 16)];
        }

        return color;
    }

    public addRandomColoredGeoJSON(map: MaplibreMap, geojson: GeoJSON.FeatureCollection<GeoJSON.Geometry>): void {
        const coloredGeoJSON = {
            ...geojson,
            features: geojson.features.map((feature) => ({
                ...feature,
                properties: {
                    ...feature.properties,
                    fillColor: this.getRandomColor(),
                },
            })),
        };

        const sourceId = 'random-colored-geojson-source';
        if (!map.getSource(sourceId)) {
            map.addSource(sourceId, {
                type: 'geojson',
                data: coloredGeoJSON,
            });
        } else {
            const source = map.getSource(sourceId) as maplibregl.GeoJSONSource;
            source.setData(coloredGeoJSON);
        }

        const layerId = 'random-colored-geojson-layer';
        if (!map.getLayer(layerId)) {
            map.addLayer({
                id: layerId,
                type: 'fill',
                source: sourceId,
                paint: {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    'fill-color': ['get', 'fillColor'], // Витягуємо колір з властивості фічі
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    'line-color': ['get', 'fillColor'],
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    'line-width': 1,
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    // 'fill-opacity': 0.7,
                },
            });
            map.addLayer({
                id: `${layerId}-line`,
                type: 'line',
                source: sourceId,
                paint: {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    'line-color': ['get', 'fillColor'],
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    'line-width': 1,
                },
            });
        }
    }

    public addMarksForPoints(map: MaplibreMap, geojson: GeoJSON.FeatureCollection<GeoJSON.Geometry>): void {
        geojson.features.forEach((feature) => {
            if (feature.geometry.type === 'Point') {

                new Marker()
                    .setLngLat(new LngLat(feature.geometry.coordinates[0], feature.geometry.coordinates[1]))
                    .addTo(map);
            }
        });


    }
}
