import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable()
export class MousePositionPopupService {
    public readonly isPopupVisible$ = new BehaviorSubject(false);
    public isVisible$ = this.isPopupVisible$.asObservable();

    public show(): void {
        this.isPopupVisible$.next(true);
    }

    public hide(): void {
        this.isPopupVisible$.next(false);
    }
}
