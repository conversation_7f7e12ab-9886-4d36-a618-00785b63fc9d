import { Injectable } from '@angular/core';
import * as turf from '@turf/turf';
import { GeojsonUtilsService } from './geojson-utils.service';

@Injectable()
export class PolygonTransformationService {

    constructor(
        private readonly geojsonUtils: GeojsonUtilsService,
    ) {
    }

    public buildExteriorPolygon(points: number[][]): number[][] {
        const pointsIncludedIntersections = this.obtainAndInsertIntersectionPoints(points);
        const isClockwise = turf.booleanClockwise(pointsIncludedIntersections);
        const preparedPoints = isClockwise
            ? pointsIncludedIntersections.reverse()
            : pointsIncludedIntersections;

        const lines = this.pointsToLines(preparedPoints);
        const firstLine = lines[0];
        const exteriorLines = this.findExteriorPolygonBorderLines(firstLine, lines, [firstLine]);
        const linesPoints = this.linesToPoints(exteriorLines);

        return linesPoints;
    }

    private obtainAndInsertIntersectionPoints(points: number[][]): number[][] {
        if (points.length < 2) {
            return points;
        }

        let resultPoints: number[][] = [...points];

        const intersectPoints = [];

        for (let i = 0; i < points.length - 1; i++) {
            const firstLineEndPointIndex = i + 1;
            const lineStartPoint = points[i];
            const segment1 = turf.lineString([lineStartPoint, points[firstLineEndPointIndex]]);
            const intersectPointsCoors: number[][] = [];

            for (let j = 0; j < points.length - 1; j++) {
                const secondLineEndPointIndex = j + 1;
                const segment2 = turf.lineString([points[j], points[secondLineEndPointIndex]]);
                const intersections = turf.lineIntersect(segment1, segment2);
                const isIntersections = !!intersections.features.length;
                const isSameLines = firstLineEndPointIndex === secondLineEndPointIndex;

                if (isSameLines || !isIntersections) {
                    continue;
                }

                turf.coordEach(intersections, (intersectionPoint) => {
                    const isFirstLineIncludePoint = this.isLineIncludePoint(intersectionPoint, segment1.geometry.coordinates);
                    const isSecondLineIncludePoint = this.isLineIncludePoint(intersectionPoint, segment1.geometry.coordinates);
                    const isPointBelongToLine = isFirstLineIncludePoint || isSecondLineIncludePoint;

                    if (!isPointBelongToLine) {
                        intersectPoints.push(intersectionPoint);
                        intersectPointsCoors.push(intersectionPoint);
                    }
                });
            }

            const newPointsSortedList = this.sortPointsOnLine(lineStartPoint, intersectPointsCoors);
            const addedPointsCount = resultPoints.length - points.length;
            resultPoints = [
                ...resultPoints.slice(0, firstLineEndPointIndex + addedPointsCount),
                ...newPointsSortedList,
                ...resultPoints.slice(firstLineEndPointIndex + addedPointsCount),
            ];
        }

        return resultPoints;
    }

    private distance(from: number[], to: number[]): number {
        const pointFrom = turf.point(from);
        const pointTo = turf.point(to);

        return turf.distance(pointFrom, pointTo);
    }

    private sortPointsOnLine(start: number[], points: number[][]): number[][] {
        return points.sort((a, b) => {
            const distA = this.distance(start, a);
            const distB = this.distance(start, b);

            return distA - distB;
        });
    }

    private findExteriorPolygonBorderLines(currentLine: number[][], lines: number[][][], borderLines: number[][][]): number[][][] {
        const nextLine = this.findNextLine(currentLine, lines);
        const isLineAlreadyUsed = this.isLinesListIncludeLine(borderLines, nextLine);

        if (isLineAlreadyUsed) {
            return borderLines;
        } else {
            return this.findExteriorPolygonBorderLines(nextLine, lines, [...borderLines, nextLine]);
        }
    }

    private isLinesListIncludeLine(lines: number[][][], targetLine: number[][]): boolean {
        return lines.some((line) => this.compareLines(line, targetLine, { ignoreDirection: true }));
    }

    private findNextLine(previousLine: number[][], lines: number[][][]): number[][] {
        const endPoint = previousLine[1];
        const nextPossibleLines = this.findAllLinesWithPoint(lines, endPoint, { exclude: previousLine });
        const correctedLines = nextPossibleLines.map((line) => this.rotateLineRelativeToStartPoint(endPoint, line));

        return correctedLines.length > 1
            ? this.findCounterClockwiseLineComparing(previousLine, correctedLines) || correctedLines[0]
            : correctedLines[0];
    }

    private findCounterClockwiseLineComparing(referenceLine: number[][], lines: number[][][]): number[][] | null {
        return lines.find((line) => {
            const lineA = referenceLine;
            const lineB = line;
            const vectorA = this.getVectorFromPoints(lineA[1], lineA[0]);
            const vectorB = this.getVectorFromPoints(lineB[0], lineB[1]);
            const crossProduct = this.crossProduct(vectorA, vectorB);

            return crossProduct > 0;
        }) || null;
    }

    private pointsToLines(points: number[][]): number[][][] {
        const linePoints: number[][][] = [];

        for (let i = 1; i < points.length; i++) {
            const startPoint = points[i - 1];
            const endPoint = points[i];
            const line = [startPoint, endPoint];
            linePoints.push(line);
        }

        return linePoints;
    }

    private linesToPoints(lines: number[][][]): number[][] {
        const firstLine = lines[0];
        const firstPoint = firstLine[0];
        const points: number[][] = [firstPoint];

        lines.forEach((line) => {
            const endPoint = line[1];
            points.push(endPoint);
        });

        return points;
    }

    private findAllLinesWithPoint(lines: number[][][], point: number[], { exclude }: { exclude: number[][] } | undefined = { exclude: [] }): number[][][] {
        return lines.filter((line) => {
            const isFirstPointEqual = this.comparePoints(line[0], point);
            const isSecondPointEqual = this.comparePoints(line[1], point);

            if (exclude.length) {
                const excludedLine = [exclude[0], exclude[1]];
                const isLineExcluded = this.compareLines(line, excludedLine, { ignoreDirection: true });

                if (isLineExcluded) {
                    return false;
                }
            }

            return isFirstPointEqual || isSecondPointEqual;
        });
    }

    private isLineIncludePoint(targetPoint: number[], line: number[][]): boolean {
        const isFirstPointEqual = this.comparePoints(targetPoint, line[0]);
        const isSecondPointEqual = this.comparePoints(targetPoint, line[1]);

        return isFirstPointEqual || isSecondPointEqual;
    }

    private compareLines(a: number[][], b: number[][], { ignoreDirection = false }: { ignoreDirection: boolean }): boolean {
        const firstPointA = a[0];
        const secondPointA = a[1];
        const firstPointB = b[0];
        const secondPointB = b[1];
        const isFirstPointEqual = this.comparePoints(firstPointA, firstPointB);
        const isSecondPointEqual = this.comparePoints(secondPointA, secondPointB);

        if (ignoreDirection) {
            const isFirstPointEqualReverse = this.comparePoints(firstPointA, secondPointB);
            const isSecondPointEqualReverse = this.comparePoints(secondPointA, firstPointB);

            return isFirstPointEqual && isSecondPointEqual
                || isFirstPointEqualReverse && isSecondPointEqualReverse;
        }

        return isFirstPointEqual || isSecondPointEqual;
    }

    private comparePoints(a: number[], b: number[]): boolean {
        return a[0] === b[0] && a[1] === b[1];
    }

    private rotateLineRelativeToStartPoint(startPoint: number[], line: number[][]): number[][] {
        return [startPoint, this.getOppositeLinePoint(startPoint, line)];
    }

    private getOppositeLinePoint(targetPoint: number[], line: number[][]): number[] {
        const firstPoint = line[0];
        const secondPoint = line[1];

        return this.comparePoints(firstPoint, targetPoint)
            ? secondPoint
            : firstPoint;
    }

    private getVectorFromPoints(pointA: number[], pointB: number[]): number[] {
        return [pointB[0] - pointA[0], pointB[1] - pointA[1]];
    }

    private crossProduct(vectorA: number[], vectorB: number[]): number {
        return vectorA[0] * vectorB[1] - vectorA[1] * vectorB[0];
    }

    private debugPoints(input: number[][][] | number[][]): number[][][] | number[][] {
        const transform = (arr: any): any => {
            return arr.map((item: any) => {
                if (Array.isArray(item)) {
                    return transform(item);
                } else if (typeof item === 'number') {
                    const str = item.toString();
                    const last4 = str.slice(-4);
                    return parseInt(last4, 10);
                } else {
                    throw new Error('Invalid data type in array');
                }
            });
        };

        return transform(input);
    }
}
