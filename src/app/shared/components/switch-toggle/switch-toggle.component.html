<div class="switch-toggle" (click)="onToggle()">
    <div class="switch-toggle__track" [class.switch-toggle__track--active]="isActivated">
        <mat-icon
            class="switch-toggle__icon switch-toggle__icon--left"
            [class.switch-toggle__icon--active]="isActivated"
            [svgIcon]="icon"
        ></mat-icon>

        <mat-icon
            class="switch-toggle__icon switch-toggle__icon--right"
            [class.switch-toggle__icon--active]="!isActivated"
            [svgIcon]="icon"
        ></mat-icon>

        <div class="switch-toggle__circle" [class.switch-toggle__circle--active]="isActivated">
            <mat-icon
                *ngIf="isActivated"
                class="switch-toggle__check"
                svgIcon="check-mark"
            ></mat-icon>
        </div>
    </div>
</div>
