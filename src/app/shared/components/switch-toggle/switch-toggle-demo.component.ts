import { Component } from '@angular/core';

@Component({
    selector: 'avl-switch-toggle-demo',
    template: `
        <div style="padding: 20px; font-family: Arial, sans-serif;">
            <h3>Switch Toggle Component Demo</h3>
            
            <div style="margin: 20px 0;">
                <h4>Default size:</h4>
                <avl-switch-toggle 
                    icon="sam-toggle" 
                    [isActivated]="isToggle1Active"
                    (toggleChanged)="onToggle1Changed($event)">
                </avl-switch-toggle>
                <p>Status: {{ isToggle1Active ? 'Activated' : 'Deactivated' }}</p>
            </div>

            <div style="margin: 20px 0; font-size: 1.5em;">
                <h4>Large size (1.5em):</h4>
                <avl-switch-toggle 
                    icon="circle-tool" 
                    [isActivated]="isToggle2Active"
                    (toggleChanged)="onToggle2Changed($event)">
                </avl-switch-toggle>
                <p>Status: {{ isToggle2Active ? 'Activated' : 'Deactivated' }}</p>
            </div>

            <div style="margin: 20px 0; font-size: 2em;">
                <h4>Extra large size (2em):</h4>
                <avl-switch-toggle 
                    icon="polygon-tool" 
                    [isActivated]="isToggle3Active"
                    (toggleChanged)="onToggle3Changed($event)">
                </avl-switch-toggle>
                <p>Status: {{ isToggle3Active ? 'Activated' : 'Deactivated' }}</p>
            </div>
        </div>
    `,
})
export class SwitchToggleDemoComponent {
    public isToggle1Active = false;
    public isToggle2Active = true;
    public isToggle3Active = false;

    public onToggle1Changed(isActive: boolean): void {
        this.isToggle1Active = isActive;
        console.log('Toggle 1 changed:', isActive);
    }

    public onToggle2Changed(isActive: boolean): void {
        this.isToggle2Active = isActive;
        console.log('Toggle 2 changed:', isActive);
    }

    public onToggle3Changed(isActive: boolean): void {
        this.isToggle3Active = isActive;
        console.log('Toggle 3 changed:', isActive);
    }
}
